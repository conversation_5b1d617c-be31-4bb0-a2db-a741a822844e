#!/usr/bin/env python3
"""
Test script for PDF text extraction functionality
"""

import os
import sys

# Import the ConversationAnalyzer class
sys.path.append('.')
with open('conversation-analyzer-python.py', 'r', encoding='utf-8') as f:
    exec(f.read())

def test_pdf_extraction():
    """Test PDF text extraction with sample files"""
    print("=== Testing PDF Text Extraction ===")
    
    analyzer = ConversationAnalyzer()
    
    # Get list of PDF files
    pdf_files = [f for f in os.listdir('PDFs') if f.endswith('.pdf')]
    
    if not pdf_files:
        print("✗ No PDF files found in PDFs directory")
        return False
    
    print(f"Found {len(pdf_files)} PDF files")
    
    # Test with first few files
    test_files = pdf_files[:3]  # Test first 3 files
    
    success_count = 0
    for pdf_file in test_files:
        pdf_path = os.path.join('PDFs', pdf_file)
        print(f"\nTesting: {pdf_file}")
        
        try:
            text = analyzer.extract_text_from_pdf(pdf_path)
            if text and len(text) > 0:
                print(f"✓ Successfully extracted {len(text)} characters")
                print(f"  First 100 chars: {text[:100].replace(chr(10), ' ')[:100]}...")
                success_count += 1
            else:
                print(f"✗ No text extracted from {pdf_file}")
        except Exception as e:
            print(f"✗ Error extracting from {pdf_file}: {str(e)}")
    
    print(f"\n=== PDF Extraction Test Results ===")
    print(f"Files tested: {len(test_files)}")
    print(f"Successful extractions: {success_count}")
    print(f"Success rate: {success_count/len(test_files)*100:.1f}%")
    
    return success_count > 0

if __name__ == "__main__":
    test_pdf_extraction()
