#!/usr/bin/env python3
"""
Analyze the actual structure of the PDF files to understand the format
"""

import os
import PyPDF2
import re

def analyze_pdf_structure():
    """Analyze the structure of PDF files to understand conversation format"""
    print("=== Analyzing PDF Structure ===")
    
    pdf_files = [f for f in os.listdir('PDFs') if f.endswith('.pdf')]
    
    # Analyze first few files
    for pdf_file in pdf_files[:3]:
        pdf_path = os.path.join('PDFs', pdf_file)
        print(f"\n--- Analyzing: {pdf_file} ---")
        
        try:
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                
                print(f"Total text length: {len(text)}")
                
                # Look for various patterns that might indicate conversations
                patterns = {
                    'Email addresses': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                    'Dates': r'\w+\s+\d{1,2},\s+\d{4}',
                    'Times': r'\d{1,2}:\d{2}:\d{2}\s*[AP]M',
                    'Names with emails': r'([A-Za-z\s\.]+)\s*<[^>]+>',
                    'Status updates': r'Status Update:\s*"([^"]+)"',
                    'Numbers (potential claim IDs)': r'\b\d{8,}\b',
                    'Company names': r'(USAA|ContractorConnection|Contractor Connection)',
                }
                
                for pattern_name, pattern in patterns.items():
                    matches = re.findall(pattern, text, re.IGNORECASE)
                    print(f"  {pattern_name}: {len(matches)} matches")
                    if matches and len(matches) <= 10:
                        print(f"    Examples: {matches[:3]}")
                
                # Show first 500 characters to understand structure
                print(f"\nFirst 500 characters:")
                print(repr(text[:500]))
                
        except Exception as e:
            print(f"Error analyzing {pdf_file}: {str(e)}")

if __name__ == "__main__":
    analyze_pdf_structure()
