#!/usr/bin/env python3
"""
Test script for conversation extraction functionality
"""

import sys
import os
import re
from datetime import datetime
from collections import defaultdict

# Import required modules
import PyPDF2
import pandas as pd

# Define the ConversationAnalyzer class inline for testing
class ConversationAnalyzer:
    def __init__(self):
        # Sentiment indicators
        self.positive_words = [
            'thank', 'appreciate', 'excellent', 'great', 'helpful', 'pleased',
            'happy', 'satisfied', 'wonderful', 'perfect', 'warmest regards',
            'best', 'good', 'fantastic', 'amazing', 'outstanding'
        ]
        
        self.negative_words = [
            'urgent', 'frustrated', 'disappointed', 'unacceptable', 'fail',
            'wrong', 'mistake', 'error', 'complaint', 'issue', 'problem',
            'delay', 'late', 'missing', 'incorrect', 'refused', 'denied'
        ]
        
        self.very_negative_words = [
            'threat', 'legal', 'lawsuit', 'terrible', 'worst', 'disaster',
            'completely unacceptable', 'refuse', 'demand'
        ]
        
        self.escalation_words = [
            'escalate', 'escalation', 'escalating', 'escalated',
            'third day', 'third-day', '3rd day', '3-day'
        ]
        
        self.cooperative_words = [
            'please', 'kindly', 'assist', 'help', 'coordinate', 'collaborate',
            'update', 'inform', 'acknowledge', 'confirm', 'approved'
        ]
        
        self.rubric = {
            1: {"label": "Very Negative", "description": "Hostile language, threats, refusal to cooperate"},
            2: {"label": "Negative", "description": "Frustrated tone, complaints, minimal cooperation"},
            3: {"label": "Neutral", "description": "Factual, business-like, standard communication"},
            4: {"label": "Positive", "description": "Professional, constructive, solution-oriented"},
            5: {"label": "Very Positive", "description": "Exceptionally helpful, proactive, excellent collaboration"}
        }

    def analyze_sentiment(self, text):
        """Analyze conversation sentiment and detect escalations"""
        lower_text = text.lower()
        
        # Count occurrences
        positive_count = sum(lower_text.count(word) for word in self.positive_words)
        negative_count = sum(lower_text.count(word) for word in self.negative_words)
        very_negative_count = sum(lower_text.count(word) for word in self.very_negative_words)
        cooperative_count = sum(lower_text.count(word) for word in self.cooperative_words)
        escalation_count = sum(lower_text.count(word) for word in self.escalation_words)
        
        has_escalation = escalation_count > 0
        
        # Determine score
        score = 3  # Default neutral
        
        if very_negative_count > 2 or has_escalation:
            score = 1 if has_escalation else 2
        elif negative_count > positive_count * 2:
            score = 2
        elif positive_count > negative_count * 2:
            score = 5 if cooperative_count > 5 else 4
        elif cooperative_count > 3:
            score = 4
            
        return score, has_escalation

    def extract_conversations(self, text, filename):
        """Extract individual conversations from PDF text"""
        conversations = []
        
        # Split by claim numbers or conversation IDs
        claim_pattern = r'Claim\s+(\d+)|Conversation ID:\s*([^\n]+)'
        sections = re.split(claim_pattern, text, flags=re.IGNORECASE)
        
        for i in range(0, len(sections), 3):
            if i + 2 < len(sections):
                claim_id = sections[i + 1] or sections[i + 2] or f"Unknown_{i}"
                content = sections[i + 2] if i + 2 < len(sections) else ""

                if content and len(content) > 50:
                    # Extract messages
                    messages = self.extract_messages(content)
                    
                    if messages:
                        participants = list(set([m['sender'] for m in messages if m['sender']]))
                        score, has_escalation = self.analyze_sentiment(content)
                        
                        conversation = {
                            'filename': filename,
                            'claim_id': claim_id.strip(),
                            'participants': participants,
                            'messages': messages,
                            'score': score,
                            'rating': self.rubric[score]['label'],
                            'has_escalation': has_escalation,
                            'message_count': len(messages),
                            'content_preview': content[:500]
                        }
                        
                        conversation['summary'] = self.generate_summary(conversation)
                        conversations.append(conversation)
        
        return conversations

    def extract_messages(self, content):
        """Extract individual messages from conversation content"""
        messages = []
        
        # Pattern to match dates and extract messages
        date_pattern = r'(\w+\s+\d{1,2},\s+\d{4})\s+(\d{1,2}:\d{2}:\d{2}\s*[AP]M)?'
        sender_pattern = r'([A-Za-z\s\.]+?)(?:<[^>]+>)?(?:\n|$)'
        
        # Split content by dates
        date_splits = re.split(date_pattern, content)
        
        for i in range(1, len(date_splits), 3):
            if i + 2 < len(date_splits):
                date = date_splits[i]
                time = date_splits[i + 1] or ""
                message_content = date_splits[i + 2]
                
                # Extract sender from message content
                sender_match = re.match(sender_pattern, message_content.strip())
                if sender_match:
                    sender = sender_match.group(1).strip()
                    msg_text = message_content[len(sender):].strip()
                    
                    messages.append({
                        'date': f"{date} {time}".strip(),
                        'sender': sender,
                        'content': msg_text[:500]  # Limit message length
                    })
        
        return messages

    def generate_summary(self, conversation):
        """Generate a summary for the conversation"""
        messages = conversation['messages']
        if not messages:
            return "No messages found in conversation."
        
        first_msg = messages[0]
        last_msg = messages[-1]
        
        # Build summary
        summary = f"This conversation regarding Claim {conversation['claim_id']} "
        summary += f"involves {len(conversation['participants'])} participants "
        summary += f"exchanging {len(messages)} messages. "
        
        # Add sentiment analysis
        if conversation['score'] <= 2:
            summary += "The conversation exhibits signs of frustration and communication difficulties. "
        elif conversation['score'] >= 4:
            summary += "The conversation maintains a professional and cooperative tone throughout. "
        else:
            summary += "The conversation remains mostly neutral and procedural. "
        
        # Add escalation note
        if conversation['has_escalation']:
            summary += "ESCALATION DETECTED: This conversation contains escalation language. "
        
        # Add date range
        summary += f"The exchange spans from {first_msg['date']} to {last_msg['date']}."
        
        return summary

def test_conversation_extraction():
    """Test conversation extraction with mock data"""
    print("=== Testing Conversation Extraction ===")
    
    analyzer = ConversationAnalyzer()
    
    # Mock PDF text with expected format
    mock_text = """
    Some header content here.
    
    Claim 12345
    
    January 15, 2024 10:30:00 AM
    John Smith <<EMAIL>>
    Thank you for your help with this claim. I appreciate your assistance.
    
    January 15, 2024 2:45:00 PM  
    Jane Doe <<EMAIL>>
    You're welcome! I'm happy to help coordinate this matter.
    
    Conversation ID: CONV-67890
    
    February 20, 2024 9:15:00 AM
    Bob Johnson <<EMAIL>>
    This is unacceptable! There has been a delay and I am frustrated with this error.
    
    February 20, 2024 11:30:00 AM
    Alice Wilson <<EMAIL>>
    I understand your frustration. Let me escalate this to resolve the issue.
    """
    
    print("Testing with mock conversation data...")
    conversations = analyzer.extract_conversations(mock_text, "test_file.pdf")
    
    print(f"Found {len(conversations)} conversations")
    
    for i, conv in enumerate(conversations, 1):
        print(f"\nConversation {i}:")
        print(f"  Claim ID: {conv['claim_id']}")
        print(f"  Participants: {conv['participants']}")
        print(f"  Messages: {conv['message_count']}")
        print(f"  Score: {conv['score']} ({conv['rating']})")
        print(f"  Escalation: {conv['has_escalation']}")
        print(f"  Summary: {conv['summary'][:100]}...")
    
    # Test message extraction specifically
    print(f"\n=== Testing Message Extraction ===")
    test_content = """
    January 15, 2024 10:30:00 AM
    John Smith <<EMAIL>>
    This is the first message content.
    
    January 16, 2024 2:45:00 PM  
    Jane Doe
    This is the second message.
    """
    
    messages = analyzer.extract_messages(test_content)
    print(f"Extracted {len(messages)} messages:")
    for msg in messages:
        print(f"  {msg['date']} - {msg['sender']}: {msg['content'][:50]}...")
    
    print(f"\n=== Conversation Extraction Test Results ===")
    print(f"Conversations found: {len(conversations)}")
    print(f"Message extraction working: {'✓' if len(messages) > 0 else '✗'}")
    
    return len(conversations) > 0 and len(messages) > 0

if __name__ == "__main__":
    test_conversation_extraction()
