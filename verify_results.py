#!/usr/bin/env python3
"""
Verify the Excel results and show a sample
"""

import pandas as pd

def verify_excel_results():
    """Verify and display sample results from the Excel file"""
    try:
        # Read the Excel file
        df = pd.read_excel('OutPuts/conversation_analysis_results.xlsx', sheet_name='Conversation Analysis')
        
        print("=== Excel File Verification ===")
        print(f"Total conversations analyzed: {len(df)}")
        print(f"Columns: {list(df.columns)}")
        print()
        
        # Show rating distribution
        print("Rating Distribution:")
        ratings = df['Rating'].value_counts().sort_index()
        for rating, count in ratings.items():
            print(f"  {rating}: {count} conversations")
        print()
        
        # Show sample entries
        print("Sample Entries:")
        print("=" * 80)
        
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"Claim Number: {row['Claim Number']}")
            print(f"Rating: {row['Rating']}")
            print(f"Explanation: {row['Rating Explanation'][:100]}...")
            print(f"Summary: {row['Conversation Summary'][:150]}...")
            print("-" * 80)
        
        print(f"\n✓ Excel file successfully created with {len(df)} conversations!")
        print("✓ All requested columns are present:")
        print("  - Claim Number")
        print("  - Rating") 
        print("  - Rating Explanation")
        print("  - Conversation Summary")
        
    except Exception as e:
        print(f"Error reading Excel file: {str(e)}")

if __name__ == "__main__":
    verify_excel_results()
