#!/usr/bin/env python3
"""
Test script for sentiment analysis functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

# Import required modules
import PyPDF2
import pandas as pd
from datetime import datetime
from collections import defaultdict
import re
import json
import argparse

# Define the ConversationAnalyzer class inline for testing
class ConversationAnalyzer:
    def __init__(self):
        # Sentiment indicators
        self.positive_words = [
            'thank', 'appreciate', 'excellent', 'great', 'helpful', 'pleased',
            'happy', 'satisfied', 'wonderful', 'perfect', 'warmest regards',
            'best', 'good', 'fantastic', 'amazing', 'outstanding'
        ]
        
        self.negative_words = [
            'urgent', 'frustrated', 'disappointed', 'unacceptable', 'fail',
            'wrong', 'mistake', 'error', 'complaint', 'issue', 'problem',
            'delay', 'late', 'missing', 'incorrect', 'refused', 'denied'
        ]
        
        self.very_negative_words = [
            'threat', 'legal', 'lawsuit', 'terrible', 'worst', 'disaster',
            'completely unacceptable', 'refuse', 'demand'
        ]
        
        self.escalation_words = [
            'escalate', 'escalation', 'escalating', 'escalated',
            'third day', 'third-day', '3rd day', '3-day'
        ]
        
        self.cooperative_words = [
            'please', 'kindly', 'assist', 'help', 'coordinate', 'collaborate',
            'update', 'inform', 'acknowledge', 'confirm', 'approved'
        ]
        
        self.rubric = {
            1: {"label": "Very Negative", "description": "Hostile language, threats, refusal to cooperate"},
            2: {"label": "Negative", "description": "Frustrated tone, complaints, minimal cooperation"},
            3: {"label": "Neutral", "description": "Factual, business-like, standard communication"},
            4: {"label": "Positive", "description": "Professional, constructive, solution-oriented"},
            5: {"label": "Very Positive", "description": "Exceptionally helpful, proactive, excellent collaboration"}
        }

    def analyze_sentiment(self, text):
        """Analyze conversation sentiment and detect escalations"""
        lower_text = text.lower()
        
        # Count occurrences
        positive_count = sum(lower_text.count(word) for word in self.positive_words)
        negative_count = sum(lower_text.count(word) for word in self.negative_words)
        very_negative_count = sum(lower_text.count(word) for word in self.very_negative_words)
        cooperative_count = sum(lower_text.count(word) for word in self.cooperative_words)
        escalation_count = sum(lower_text.count(word) for word in self.escalation_words)
        
        has_escalation = escalation_count > 0
        
        # Determine score
        score = 3  # Default neutral
        
        if very_negative_count > 2 or has_escalation:
            score = 1 if has_escalation else 2
        elif negative_count > positive_count * 2:
            score = 2
        elif positive_count > negative_count * 2:
            score = 5 if cooperative_count > 5 else 4
        elif cooperative_count > 3:
            score = 4
            
        return score, has_escalation

def test_sentiment_analysis():
    """Test sentiment analysis with various text samples"""
    print("=== Testing Sentiment Analysis Engine ===")
    
    analyzer = ConversationAnalyzer()
    
    # Test cases with expected results
    test_cases = [
        {
            "text": "Thank you so much for your excellent help! This was fantastic and I really appreciate your outstanding service.",
            "expected_score": 5,
            "description": "Very positive text"
        },
        {
            "text": "Please help me coordinate this. I would appreciate your assistance and collaboration on this matter.",
            "expected_score": 4,
            "description": "Positive cooperative text"
        },
        {
            "text": "This is a standard business communication regarding the status update.",
            "expected_score": 3,
            "description": "Neutral business text"
        },
        {
            "text": "I am frustrated with this delay. There was an error and this is unacceptable. This problem needs to be fixed.",
            "expected_score": 2,
            "description": "Negative frustrated text"
        },
        {
            "text": "This is terrible! I refuse to accept this disaster. I demand legal action and will escalate this immediately!",
            "expected_score": 1,
            "description": "Very negative with escalation"
        },
        {
            "text": "We need to escalate this to the third day review process.",
            "expected_score": 1,
            "description": "Escalation keywords"
        }
    ]
    
    print(f"Running {len(test_cases)} test cases...\n")
    
    passed = 0
    for i, test_case in enumerate(test_cases, 1):
        score, has_escalation = analyzer.analyze_sentiment(test_case["text"])
        expected = test_case["expected_score"]
        
        status = "✓ PASS" if score == expected else "✗ FAIL"
        print(f"Test {i}: {test_case['description']}")
        print(f"  Expected: {expected} ({analyzer.rubric[expected]['label']})")
        print(f"  Got: {score} ({analyzer.rubric[score]['label']})")
        print(f"  Escalation: {has_escalation}")
        print(f"  Status: {status}")
        print()
        
        if score == expected:
            passed += 1
    
    print(f"=== Sentiment Analysis Test Results ===")
    print(f"Tests passed: {passed}/{len(test_cases)}")
    print(f"Success rate: {passed/len(test_cases)*100:.1f}%")
    
    return passed == len(test_cases)

if __name__ == "__main__":
    test_sentiment_analysis()
