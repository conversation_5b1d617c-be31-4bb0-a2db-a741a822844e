#!/usr/bin/env python3
"""
Test script for export functionality
"""

import os
import pandas as pd

def test_export_functionality():
    """Test CSV and Excel export with mock data"""
    print("=== Testing Export Functionality ===")
    
    # Create mock conversation data
    mock_conversations = [
        {
            'filename': 'test1.pdf',
            'claim_id': '12345',
            'score': 4,
            'rating': 'Positive',
            'has_escalation': False,
            'participants': ['<PERSON>', '<PERSON>'],
            'message_count': 3,
            'messages': [
                {'date': 'January 15, 2024 10:30:00 AM', 'sender': '<PERSON>', 'content': 'Thank you for your help'},
                {'date': 'January 15, 2024 2:45:00 PM', 'sender': '<PERSON>', 'content': 'You are welcome'},
                {'date': 'January 16, 2024 9:00:00 AM', 'sender': '<PERSON>', 'content': 'Great service'}
            ],
            'summary': 'This conversation regarding Claim 12345 involves 2 participants exchanging 3 messages. The conversation maintains a professional and cooperative tone throughout.'
        },
        {
            'filename': 'test2.pdf',
            'claim_id': '67890',
            'score': 2,
            'rating': 'Negative',
            'has_escalation': True,
            'participants': ['<PERSON>', '<PERSON>'],
            'message_count': 2,
            'messages': [
                {'date': 'February 20, 2024 9:15:00 AM', 'sender': 'Bob Johnson', 'content': 'This is unacceptable! I need to escalate this.'},
                {'date': 'February 20, 2024 11:30:00 AM', 'sender': 'Alice Wilson', 'content': 'I understand your frustration.'}
            ],
            'summary': 'This conversation regarding Claim 67890 involves 2 participants exchanging 2 messages. The conversation exhibits signs of frustration and communication difficulties. ESCALATION DETECTED: This conversation contains escalation language.'
        }
    ]
    
    # Test CSV export
    print("Testing CSV export...")
    try:
        # Prepare data for DataFrame
        data = []
        for conv in mock_conversations:
            data.append({
                'Filename': conv['filename'],
                'Claim ID': conv['claim_id'],
                'Score': conv['score'],
                'Rating': conv['rating'],
                'Has Escalation': 'Yes' if conv['has_escalation'] else 'No',
                'Participants': ', '.join(conv['participants'][:3]),
                'Message Count': conv['message_count'],
                'First Message Date': conv['messages'][0]['date'] if conv['messages'] else 'N/A',
                'Last Message Date': conv['messages'][-1]['date'] if conv['messages'] else 'N/A',
                'Summary': conv['summary']
            })
        
        df = pd.DataFrame(data)
        csv_file = "test_export_results.csv"
        df.to_csv(csv_file, index=False)
        
        if os.path.exists(csv_file):
            print(f"✓ CSV export successful: {csv_file}")
            print(f"  File size: {os.path.getsize(csv_file)} bytes")
            
            # Verify content
            df_read = pd.read_csv(csv_file)
            print(f"  Rows exported: {len(df_read)}")
            print(f"  Columns: {list(df_read.columns)}")
        else:
            print("✗ CSV export failed")
            
    except Exception as e:
        print(f"✗ CSV export error: {str(e)}")
    
    # Test Excel export
    print("\nTesting Excel export...")
    try:
        excel_file = "test_export_results.xlsx"
        
        # Create Excel writer with formatting
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Analysis Results', index=False)
            
            # Add summary sheet
            summary_data = {
                'Metric': ['Total Conversations', 'Files Processed', 'Very Negative (1)', 
                          'Negative (2)', 'Neutral (3)', 'Positive (4)', 'Very Positive (5)', 
                          'With Escalations'],
                'Count': [
                    len(mock_conversations),
                    len(set(conv['filename'] for conv in mock_conversations)),
                    sum(1 for conv in mock_conversations if conv['score'] == 1),
                    sum(1 for conv in mock_conversations if conv['score'] == 2),
                    sum(1 for conv in mock_conversations if conv['score'] == 3),
                    sum(1 for conv in mock_conversations if conv['score'] == 4),
                    sum(1 for conv in mock_conversations if conv['score'] == 5),
                    sum(1 for conv in mock_conversations if conv['has_escalation'])
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        if os.path.exists(excel_file):
            print(f"✓ Excel export successful: {excel_file}")
            print(f"  File size: {os.path.getsize(excel_file)} bytes")
            
            # Verify content
            df_read = pd.read_excel(excel_file, sheet_name='Analysis Results')
            summary_read = pd.read_excel(excel_file, sheet_name='Summary')
            print(f"  Analysis sheet rows: {len(df_read)}")
            print(f"  Summary sheet rows: {len(summary_read)}")
        else:
            print("✗ Excel export failed")
            
    except Exception as e:
        print(f"✗ Excel export error: {str(e)}")
    
    # Print summary statistics
    print(f"\n=== Export Test Summary ===")
    print(f"Mock conversations created: {len(mock_conversations)}")
    print(f"CSV export: {'✓' if os.path.exists(csv_file) else '✗'}")
    print(f"Excel export: {'✓' if os.path.exists(excel_file) else '✗'}")
    
    # Clean up test files
    for test_file in [csv_file, excel_file]:
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
                print(f"Cleaned up: {test_file}")
            except:
                print(f"Could not clean up: {test_file}")
    
    return os.path.exists(csv_file) or os.path.exists(excel_file)

if __name__ == "__main__":
    test_export_functionality()
