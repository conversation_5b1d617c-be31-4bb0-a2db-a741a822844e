#!/usr/bin/env python3
"""
Debug conversation extraction
"""

import re

def debug_conversation_extraction():
    mock_text = """
    Some header content here.
    
    Claim 12345
    
    January 15, 2024 10:30:00 AM
    <PERSON> <<EMAIL>>
    Thank you for your help with this claim. I appreciate your assistance.
    
    January 15, 2024 2:45:00 PM  
    <PERSON> <<EMAIL>>
    You're welcome! I'm happy to help coordinate this matter.
    
    Conversation ID: CONV-67890
    
    February 20, 2024 9:15:00 AM
    <PERSON> <<EMAIL>>
    This is unacceptable! There has been a delay and I am frustrated with this error.
    
    February 20, 2024 11:30:00 AM
    <PERSON> <<EMAIL>>
    I understand your frustration. Let me escalate this to resolve the issue.
    """
    
    print("=== Debugging Conversation Extraction ===")
    print("Original text:")
    print(repr(mock_text[:200]))
    print()
    
    # Test the regex pattern
    claim_pattern = r'Claim\s+(\d+)|Conversation ID:\s*([^\n]+)'
    matches = re.findall(claim_pattern, mock_text, flags=re.IGNORECASE)
    print(f"Regex matches found: {matches}")
    
    # Test the split
    sections = re.split(claim_pattern, mock_text, flags=re.IGNORECASE)
    print(f"Split sections: {len(sections)}")
    for i, section in enumerate(sections):
        print(f"Section {i}: {repr(section[:100] if section else None)}")
    
    print("\nProcessing sections:")
    for i in range(0, len(sections), 3):
        print(f"Processing index {i}")
        if i + 2 < len(sections):
            claim_id = sections[i + 1] or sections[i + 2] or f"Unknown_{i}"
            content = sections[i + 2] if i + 2 < len(sections) else ""
            print(f"  Claim ID: {repr(claim_id)}")
            print(f"  Content length: {len(content) if content else 0}")
            print(f"  Content preview: {repr(content[:100]) if content else 'None'}")
        print()

if __name__ == "__main__":
    debug_conversation_extraction()
