#!/usr/bin/env python3
"""
Improved PDF Conversation Analyzer
Analyzes conversations in PDF files and ranks them based on sentiment (1-5 scale)
Adapted for the actual PDF format found in the files.
"""

import os
import re
import pandas as pd
import PyPDF2
from datetime import datetime

class ImprovedConversationAnalyzer:
    def __init__(self):
        # Sentiment indicators
        self.positive_words = [
            'thank', 'appreciate', 'excellent', 'great', 'helpful', 'pleased',
            'happy', 'satisfied', 'wonderful', 'perfect', 'warmest regards',
            'best', 'good', 'fantastic', 'amazing', 'outstanding', 'approved',
            'accepted', 'completed', 'resolved', 'successful'
        ]
        
        self.negative_words = [
            'urgent', 'frustrated', 'disappointed', 'unacceptable', 'fail',
            'wrong', 'mistake', 'error', 'complaint', 'issue', 'problem',
            'delay', 'late', 'missing', 'incorrect', 'refused', 'denied',
            'exception', 'pending', 'cancelled', 'rejected'
        ]
        
        self.very_negative_words = [
            'threat', 'legal', 'lawsuit', 'terrible', 'worst', 'disaster',
            'completely unacceptable', 'refuse', 'demand', 'escalate'
        ]
        
        self.cooperative_words = [
            'please', 'kindly', 'assist', 'help', 'coordinate', 'collaborate',
            'update', 'inform', 'acknowledge', 'confirm', 'approved', 'agree',
            'work with', 'certify', 'review'
        ]
        
        self.rubric = {
            1: {"label": "Very Negative", "description": "Hostile language, threats, refusal to cooperate"},
            2: {"label": "Negative", "description": "Frustrated tone, complaints, minimal cooperation"},
            3: {"label": "Neutral", "description": "Factual, business-like, standard communication"},
            4: {"label": "Positive", "description": "Professional, constructive, solution-oriented"},
            5: {"label": "Very Positive", "description": "Exceptionally helpful, proactive, excellent collaboration"}
        }

    def extract_claim_number(self, filename):
        """Extract claim number from filename"""
        # Look for patterns like 002688054, 002727886, etc.
        match = re.search(r'(\d{8,})', filename)
        if match:
            return match.group(1)
        
        # Look for other patterns
        match = re.search(r'([A-Z0-9]{8,})', filename)
        if match:
            return match.group(1)
        
        return filename.replace('.pdf', '')

    def extract_text_from_pdf(self, pdf_path):
        """Extract text content from PDF file"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"Error reading {pdf_path}: {str(e)}")
            return ""

    def analyze_sentiment(self, text):
        """Analyze conversation sentiment"""
        lower_text = text.lower()
        
        # Count occurrences
        positive_count = sum(lower_text.count(word) for word in self.positive_words)
        negative_count = sum(lower_text.count(word) for word in self.negative_words)
        very_negative_count = sum(lower_text.count(word) for word in self.very_negative_words)
        cooperative_count = sum(lower_text.count(word) for word in self.cooperative_words)
        
        # Look for specific status indicators
        status_updates = re.findall(r'Status Update:\s*"([^"]+)"', text, re.IGNORECASE)
        
        # Determine score based on content
        score = 3  # Default neutral
        
        # Check for very negative indicators
        if very_negative_count > 0:
            score = 1
        elif 'cancelled' in lower_text or 'rejected' in lower_text:
            score = 2
        elif 'exception' in lower_text and negative_count > positive_count:
            score = 2
        elif 'approved' in lower_text or 'accepted' in lower_text:
            if cooperative_count > 3:
                score = 5
            else:
                score = 4
        elif positive_count > negative_count * 1.5:
            score = 4
        elif negative_count > positive_count * 1.5:
            score = 2
        
        return score, status_updates

    def extract_participants(self, text):
        """Extract participant names and companies"""
        participants = []
        
        # Find names with email addresses
        name_email_pattern = r'([A-Za-z\s\.]+)\s*<([^>]+)>'
        matches = re.findall(name_email_pattern, text)
        
        for name, email in matches:
            name = name.strip()
            if name and len(name) > 2:
                # Determine company from email domain
                company = "Unknown"
                if 'usaa.com' in email:
                    company = "USAA"
                elif 'contractorconnection.com' in email:
                    company = "ContractorConnection"
                
                participant_info = f"{name} ({company})"
                if participant_info not in participants:
                    participants.append(participant_info)
        
        return participants[:5]  # Limit to first 5 participants

    def extract_timeline(self, text):
        """Extract timeline information"""
        # Find dates
        date_pattern = r'(\w+\s+\d{1,2},\s+\d{4})'
        dates = re.findall(date_pattern, text)
        
        if dates:
            return dates[0], dates[-1]
        return "Unknown", "Unknown"

    def generate_summary(self, filename, text, score, status_updates, participants):
        """Generate a comprehensive summary"""
        claim_number = self.extract_claim_number(filename)
        first_date, last_date = self.extract_timeline(text)
        
        # Count interactions
        interaction_count = len(re.findall(r'\d+[A-Z]+', text))  # Count numbered entries
        
        summary = f"Claim {claim_number} involves {len(participants)} participants "
        summary += f"with {interaction_count} recorded interactions. "
        
        # Add timeline
        if first_date != "Unknown" and last_date != "Unknown":
            summary += f"Activity spans from {first_date} to {last_date}. "
        
        # Add sentiment analysis
        if score <= 2:
            summary += "The conversation shows challenges with "
            if 'cancelled' in text.lower():
                summary += "collaboration being cancelled. "
            elif 'exception' in text.lower():
                summary += "exceptions requiring additional review. "
            else:
                summary += "communication difficulties. "
        elif score >= 4:
            summary += "The conversation demonstrates positive collaboration with "
            if 'approved' in text.lower():
                summary += "successful approvals achieved. "
            else:
                summary += "professional cooperation throughout. "
        else:
            summary += "The conversation follows standard procedural communication. "
        
        # Add status information
        if status_updates:
            final_status = status_updates[-1] if status_updates else "Unknown"
            summary += f"Final status: {final_status}."
        
        return summary

    def get_rating_explanation(self, score, text, status_updates):
        """Generate explanation for the rating"""
        explanation = f"Rated {score}/5 ({self.rubric[score]['label']}) because "
        
        if score == 1:
            explanation += "the conversation contains hostile language, threats, or complete refusal to cooperate."
        elif score == 2:
            if 'cancelled' in text.lower():
                explanation += "collaboration was cancelled, indicating communication breakdown."
            elif 'exception' in text.lower():
                explanation += "multiple exceptions were noted, suggesting ongoing issues."
            else:
                explanation += "the tone shows frustration and minimal cooperation."
        elif score == 3:
            explanation += "the communication is factual and business-like without strong positive or negative indicators."
        elif score == 4:
            explanation += "the conversation shows professional cooperation and constructive problem-solving."
        elif score == 5:
            explanation += "the interaction demonstrates exceptional collaboration with multiple approvals and positive outcomes."
        
        return explanation

    def process_pdf_file(self, pdf_path):
        """Process a single PDF file"""
        filename = os.path.basename(pdf_path)
        print(f"Processing: {filename}")
        
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            return None
        
        claim_number = self.extract_claim_number(filename)
        score, status_updates = self.analyze_sentiment(text)
        participants = self.extract_participants(text)
        summary = self.generate_summary(filename, text, score, status_updates, participants)
        explanation = self.get_rating_explanation(score, text, status_updates)
        
        return {
            'Claim Number': claim_number,
            'Rating': f"{score}/5 ({self.rubric[score]['label']})",
            'Rating Explanation': explanation,
            'Conversation Summary': summary,
            'Participants': '; '.join(participants),
            'Filename': filename
        }

    def process_all_pdfs(self, pdf_directory="PDFs"):
        """Process all PDF files in the directory"""
        results = []
        
        pdf_files = [f for f in os.listdir(pdf_directory) if f.endswith('.pdf')]
        print(f"Found {len(pdf_files)} PDF files to process...")
        
        for i, pdf_file in enumerate(pdf_files, 1):
            pdf_path = os.path.join(pdf_directory, pdf_file)
            print(f"Processing {i}/{len(pdf_files)}: {pdf_file}")
            
            result = self.process_pdf_file(pdf_path)
            if result:
                results.append(result)
        
        return results

    def export_to_excel(self, results, output_file="OutPuts/conversation_analysis_results.xlsx"):
        """Export results to Excel with the requested format"""
        if not results:
            print("No results to export.")
            return
        
        # Create DataFrame with the exact columns requested
        df = pd.DataFrame(results)
        
        # Reorder columns to match request
        column_order = ['Claim Number', 'Rating', 'Rating Explanation', 'Conversation Summary']
        df = df[column_order + [col for col in df.columns if col not in column_order]]
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Export to Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Conversation Analysis', index=False)
            
            # Auto-adjust column widths
            worksheet = writer.sheets['Conversation Analysis']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"\n✓ Results exported to: {output_file}")
        print(f"✓ Processed {len(results)} conversations")
        
        # Print summary statistics
        ratings = [int(result['Rating'].split('/')[0]) for result in results]
        print(f"\nRating Distribution:")
        for rating in range(1, 6):
            count = ratings.count(rating)
            percentage = (count / len(ratings)) * 100 if ratings else 0
            print(f"  {rating}/5: {count} conversations ({percentage:.1f}%)")

def main():
    print("=== Improved PDF Conversation Analyzer ===")
    print("Analyzing conversations and generating Excel report...\n")
    
    analyzer = ImprovedConversationAnalyzer()
    results = analyzer.process_all_pdfs()
    
    if results:
        analyzer.export_to_excel(results)
        print(f"\n🎉 Analysis complete! Check the OutPuts folder for your Excel file.")
    else:
        print("No conversations could be processed.")

if __name__ == "__main__":
    main()
