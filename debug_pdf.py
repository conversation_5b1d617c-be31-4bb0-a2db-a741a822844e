#!/usr/bin/env python3
"""
Debug script to check PDF content extraction
"""

import os
import PyPDF2

def debug_pdf_content(pdf_path):
    """Debug what's actually in the PDF"""
    print(f"=== Debugging PDF: {os.path.basename(pdf_path)} ===")
    
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            print(f"Number of pages: {len(reader.pages)}")
            
            text = ""
            for i, page in enumerate(reader.pages):
                page_text = page.extract_text()
                text += page_text + "\n"
                print(f"Page {i+1} text length: {len(page_text)}")
                if i == 0 and page_text:  # Show first page sample
                    print(f"First 300 chars of page 1:")
                    print(repr(page_text[:300]))
                    print("---")
            
            print(f"Total text length: {len(text)}")
            
            # Look for conversation patterns
            import re
            claim_matches = re.findall(r'Claim\s+(\d+)', text, re.IGNORECASE)
            conv_matches = re.findall(r'Conversation ID:\s*([^\n]+)', text, re.IGNORECASE)
            
            print(f"Found {len(claim_matches)} claim patterns")
            print(f"Found {len(conv_matches)} conversation ID patterns")
            
            if claim_matches:
                print(f"Claim numbers found: {claim_matches[:5]}")
            if conv_matches:
                print(f"Conversation IDs found: {conv_matches[:5]}")
                
            return text
            
    except Exception as e:
        print(f"Error reading PDF: {str(e)}")
        return ""

def main():
    pdf_files = [f for f in os.listdir('PDFs') if f.endswith('.pdf')]
    
    # Test first 3 PDFs
    for pdf_file in pdf_files[:3]:
        pdf_path = os.path.join('PDFs', pdf_file)
        text = debug_pdf_content(pdf_path)
        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    main()
