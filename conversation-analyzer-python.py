#!/usr/bin/env python3
"""
PDF Conversation Analyzer
Analyzes conversations in PDF files and ranks them based on sentiment (1-5 scale)
Detects escalations and generates summaries for each conversation.

Requirements:
pip install PyPDF2 pandas openpyxl
"""

import os
import re
import sys
import json
import argparse
from datetime import datetime
from collections import defaultdict
import pandas as pd

try:
    import PyPDF2
except ImportError:
    print("PyPDF2 not found. Installing...")
    os.system("pip install PyPDF2")
    import PyPDF2

class ConversationAnalyzer:
    def __init__(self):
        # Sentiment indicators
        self.positive_words = [
            'thank', 'appreciate', 'excellent', 'great', 'helpful', 'pleased',
            'happy', 'satisfied', 'wonderful', 'perfect', 'warmest regards',
            'best', 'good', 'fantastic', 'amazing', 'outstanding'
        ]
        
        self.negative_words = [
            'urgent', 'frustrated', 'disappointed', 'unacceptable', 'fail',
            'wrong', 'mistake', 'error', 'complaint', 'issue', 'problem',
            'delay', 'late', 'missing', 'incorrect', 'refused', 'denied'
        ]
        
        self.very_negative_words = [
            'threat', 'legal', 'lawsuit', 'terrible', 'worst', 'disaster',
            'completely unacceptable', 'refuse', 'demand'
        ]
        
        self.escalation_words = [
            'escalate', 'escalation', 'escalating', 'escalated',
            'third day', 'third-day', '3rd day', '3-day'
        ]
        
        self.cooperative_words = [
            'please', 'kindly', 'assist', 'help', 'coordinate', 'collaborate',
            'update', 'inform', 'acknowledge', 'confirm', 'approved'
        ]
        
        self.rubric = {
            1: {
                "label": "Very Negative",
                "description": "Hostile language, threats, refusal to cooperate"
            },
            2: {
                "label": "Negative", 
                "description": "Frustrated tone, complaints, minimal cooperation"
            },
            3: {
                "label": "Neutral",
                "description": "Factual, business-like, standard communication"
            },
            4: {
                "label": "Positive",
                "description": "Professional, constructive, solution-oriented"
            },
            5: {
                "label": "Very Positive",
                "description": "Exceptionally helpful, proactive, excellent collaboration"
            }
        }

    def extract_text_from_pdf(self, pdf_path):
        """Extract text content from PDF file"""
        try:
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"Error reading {pdf_path}: {str(e)}")
            return ""

    def analyze_sentiment(self, text):
        """Analyze conversation sentiment and detect escalations"""
        lower_text = text.lower()
        
        # Count occurrences
        positive_count = sum(lower_text.count(word) for word in self.positive_words)
        negative_count = sum(lower_text.count(word) for word in self.negative_words)
        very_negative_count = sum(lower_text.count(word) for word in self.very_negative_words)
        cooperative_count = sum(lower_text.count(word) for word in self.cooperative_words)
        escalation_count = sum(lower_text.count(word) for word in self.escalation_words)
        
        has_escalation = escalation_count > 0
        
        # Determine score
        score = 3  # Default neutral
        
        if very_negative_count > 2 or has_escalation:
            score = 1 if has_escalation else 2
        elif negative_count > positive_count * 2:
            score = 2
        elif positive_count > negative_count * 2:
            score = 5 if cooperative_count > 5 else 4
        elif cooperative_count > 3:
            score = 4
            
        return score, has_escalation

    def extract_conversations(self, text, filename):
        """Extract individual conversations from PDF text"""
        conversations = []
        
        # Split by claim numbers or conversation IDs
        claim_pattern = r'Claim\s+(\d+)|Conversation ID:\s*([^\n]+)'
        sections = re.split(claim_pattern, text, flags=re.IGNORECASE)
        
        for i in range(0, len(sections), 3):
            if i + 2 < len(sections):
                claim_id = sections[i + 1] or sections[i + 2] or f"Unknown_{i}"
                content = sections[i + 2] if i + 2 < len(sections) else ""
                
                if len(content) > 50:
                    # Extract messages
                    messages = self.extract_messages(content)
                    
                    if messages:
                        participants = list(set([m['sender'] for m in messages if m['sender']]))
                        score, has_escalation = self.analyze_sentiment(content)
                        
                        conversation = {
                            'filename': filename,
                            'claim_id': claim_id.strip(),
                            'participants': participants,
                            'messages': messages,
                            'score': score,
                            'rating': self.rubric[score]['label'],
                            'has_escalation': has_escalation,
                            'message_count': len(messages),
                            'content_preview': content[:500]
                        }
                        
                        conversation['summary'] = self.generate_summary(conversation)
                        conversations.append(conversation)
        
        return conversations

    def extract_messages(self, content):
        """Extract individual messages from conversation content"""
        messages = []
        
        # Pattern to match dates and extract messages
        date_pattern = r'(\w+\s+\d{1,2},\s+\d{4})\s+(\d{1,2}:\d{2}:\d{2}\s*[AP]M)?'
        sender_pattern = r'([A-Za-z\s\.]+?)(?:<[^>]+>)?(?:\n|$)'
        
        # Split content by dates
        date_splits = re.split(date_pattern, content)
        
        for i in range(1, len(date_splits), 3):
            if i + 2 < len(date_splits):
                date = date_splits[i]
                time = date_splits[i + 1] or ""
                message_content = date_splits[i + 2]
                
                # Extract sender from message content
                sender_match = re.match(sender_pattern, message_content.strip())
                if sender_match:
                    sender = sender_match.group(1).strip()
                    msg_text = message_content[len(sender):].strip()
                    
                    messages.append({
                        'date': f"{date} {time}".strip(),
                        'sender': sender,
                        'content': msg_text[:500]  # Limit message length
                    })
        
        return messages

    def generate_summary(self, conversation):
        """Generate a summary for the conversation"""
        messages = conversation['messages']
        if not messages:
            return "No messages found in conversation."
        
        first_msg = messages[0]
        last_msg = messages[-1]
        
        # Build summary
        summary = f"This conversation regarding Claim {conversation['claim_id']} "
        summary += f"involves {len(conversation['participants'])} participants "
        summary += f"exchanging {len(messages)} messages. "
        
        # Add sentiment analysis
        if conversation['score'] <= 2:
            summary += "The conversation exhibits signs of frustration and communication difficulties. "
        elif conversation['score'] >= 4:
            summary += "The conversation maintains a professional and cooperative tone throughout. "
        else:
            summary += "The conversation remains mostly neutral and procedural. "
        
        # Add escalation note
        if conversation['has_escalation']:
            summary += "ESCALATION DETECTED: This conversation contains escalation language. "
        
        # Add date range
        summary += f"The exchange spans from {first_msg['date']} to {last_msg['date']}."
        
        return summary

    def process_single_file(self, filepath):
        """Process a single PDF file"""
        print(f"\nProcessing: {filepath}")
        filename = os.path.basename(filepath)
        
        text = self.extract_text_from_pdf(filepath)
        if not text:
            print(f"Could not extract text from {filename}")
            return []
        
        conversations = self.extract_conversations(text, filename)
        print(f"Found {len(conversations)} conversations in {filename}")
        
        return conversations

    def process_batch(self, filepaths):
        """Process multiple PDF files"""
        all_conversations = []
        
        for i, filepath in enumerate(filepaths, 1):
            print(f"\nProcessing file {i}/{len(filepaths)}: {os.path.basename(filepath)}")
            conversations = self.process_single_file(filepath)
            all_conversations.extend(conversations)
        
        return all_conversations

    def export_to_csv(self, conversations, output_file="conversation_analysis_results.csv"):
        """Export results to CSV file"""
        if not conversations:
            print("No conversations to export.")
            return
        
        # Prepare data for DataFrame
        data = []
        for conv in conversations:
            data.append({
                'Filename': conv['filename'],
                'Claim ID': conv['claim_id'],
                'Score': conv['score'],
                'Rating': conv['rating'],
                'Has Escalation': 'Yes' if conv['has_escalation'] else 'No',
                'Participants': ', '.join(conv['participants'][:3]),  # First 3 participants
                'Message Count': conv['message_count'],
                'First Message Date': conv['messages'][0]['date'] if conv['messages'] else 'N/A',
                'Last Message Date': conv['messages'][-1]['date'] if conv['messages'] else 'N/A',
                'Summary': conv['summary']
            })
        
        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False)
        print(f"\nResults exported to: {output_file}")
        
        # Print summary statistics
        print("\n=== SUMMARY STATISTICS ===")
        print(f"Total conversations analyzed: {len(conversations)}")
        print(f"Files processed: {len(set(conv['filename'] for conv in conversations))}")
        print(f"\nScore Distribution:")
        for score in range(1, 6):
            count = sum(1 for conv in conversations if conv['score'] == score)
            print(f"  {score} - {self.rubric[score]['label']}: {count}")
        print(f"\nEscalations detected: {sum(1 for conv in conversations if conv['has_escalation'])}")

    def export_to_excel(self, conversations, output_file="conversation_analysis_results.xlsx"):
        """Export results to Excel file with formatting"""
        if not conversations:
            print("No conversations to export.")
            return
        
        # Prepare data
        data = []
        for conv in conversations:
            data.append({
                'Filename': conv['filename'],
                'Claim ID': conv['claim_id'],
                'Score': conv['score'],
                'Rating': conv['rating'],
                'Has Escalation': 'Yes' if conv['has_escalation'] else 'No',
                'Participants': ', '.join(conv['participants'][:3]),
                'Message Count': conv['message_count'],
                'First Message Date': conv['messages'][0]['date'] if conv['messages'] else 'N/A',
                'Last Message Date': conv['messages'][-1]['date'] if conv['messages'] else 'N/A',
                'Summary': conv['summary']
            })
        
        df = pd.DataFrame(data)
        
        # Create Excel writer with formatting
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Analysis Results', index=False)
            
            # Add summary sheet
            summary_data = {
                'Metric': ['Total Conversations', 'Files Processed', 'Very Negative (1)', 
                          'Negative (2)', 'Neutral (3)', 'Positive (4)', 'Very Positive (5)', 
                          'With Escalations'],
                'Count': [
                    len(conversations),
                    len(set(conv['filename'] for conv in conversations)),
                    sum(1 for conv in conversations if conv['score'] == 1),
                    sum(1 for conv in conversations if conv['score'] == 2),
                    sum(1 for conv in conversations if conv['score'] == 3),
                    sum(1 for conv in conversations if conv['score'] == 4),
                    sum(1 for conv in conversations if conv['score'] == 5),
                    sum(1 for conv in conversations if conv['has_escalation'])
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        print(f"\nResults exported to Excel: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Analyze conversations in PDF files')
    parser.add_argument('files', nargs='+', help='PDF files to analyze')
    parser.add_argument('-o', '--output', default='conversation_analysis_results.csv',
                       help='Output filename (CSV or XLSX)')
    parser.add_argument('--excel', action='store_true', 
                       help='Export to Excel format instead of CSV')
    
    args = parser.parse_args()
    
    # Initialize analyzer
    analyzer = ConversationAnalyzer()
    
    # Process files
    print("=== PDF Conversation Analyzer ===")
    print(f"Processing {len(args.files)} file(s)...")
    
    if len(args.files) == 1:
        conversations = analyzer.process_single_file(args.files[0])
    else:
        conversations = analyzer.process_batch(args.files)
    
    # Export results
    if conversations:
        if args.excel or args.output.endswith('.xlsx'):
            output_file = args.output if args.output.endswith('.xlsx') else args.output.replace('.csv', '.xlsx')
            analyzer.export_to_excel(conversations, output_file)
        else:
            analyzer.export_to_csv(conversations, args.output)
        
        # Print quick summary
        print(f"\n✓ Analysis complete! Found {len(conversations)} conversations.")
        escalations = sum(1 for conv in conversations if conv['has_escalation'])
        if escalations > 0:
            print(f"⚠️  WARNING: {escalations} conversations contain escalation language!")
    else:
        print("\nNo conversations found in the provided files.")

if __name__ == "__main__":
    # If no command line arguments, run in interactive mode
    if len(sys.argv) == 1:
        print("=== PDF Conversation Analyzer - Interactive Mode ===")
        print("\nThis tool analyzes conversations in PDF files and ranks them 1-5")
        print("based on their character (negative to positive).")
        print("\nIt also detects escalations and generates summaries.\n")
        
        # Get PDF directory
        pdf_dir = input("Enter the directory containing your PDF files (or press Enter for current directory): ").strip()
        if not pdf_dir:
            pdf_dir = os.getcwd()
        
        # Find PDF files
        pdf_files = [os.path.join(pdf_dir, f) for f in os.listdir(pdf_dir) if f.endswith('.pdf')]
        
        if not pdf_files:
            print(f"No PDF files found in {pdf_dir}")
            sys.exit(1)
        
        print(f"\nFound {len(pdf_files)} PDF files:")
        for i, f in enumerate(pdf_files, 1):
            print(f"  {i}. {os.path.basename(f)}")
        
        # Ask for processing mode
        mode = input("\nProcess all files (A) or select specific files (S)? [A/S]: ").strip().upper()
        
        if mode == 'S':
            selected = input("Enter file numbers separated by commas (e.g., 1,3,5): ").strip()
            indices = [int(i.strip()) - 1 for i in selected.split(',')]
            pdf_files = [pdf_files[i] for i in indices if 0 <= i < len(pdf_files)]
        
        # Ask for output format
        format_choice = input("\nExport format - CSV (C) or Excel (E)? [C/E]: ").strip().upper()
        use_excel = format_choice == 'E'
        
        # Process files
        analyzer = ConversationAnalyzer()
        conversations = analyzer.process_batch(pdf_files)
        
        # Export results
        if conversations:
            if use_excel:
                analyzer.export_to_excel(conversations)
            else:
                analyzer.export_to_csv(conversations)
        else:
            print("\nNo conversations found.")
    else:
        main()
